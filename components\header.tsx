import { User<PERSON><PERSON>on, ClerkLoa<PERSON>, ClerkLoading } from "@clerk/nextjs";
import { LoaderIcon } from "lucide-react";

import { HeaderLogo } from "@/components/header-logo";
import { Navigation } from "@/components/navigation";
import { WelcomeMsg } from "@/components/welcome-msg";

export const Header = () => {
  return (
    <header className="bg-gradient-to-b from-blue-700 to-blue-500 px-4 py-8 lg:px-14 pb-36">
      <div className="max-w-screen-2xl mx-auto">
        <div className="w-full flex items-center justify-between mb-14">
          <div className="flex items-center lg:gap-x-16">
            <HeaderLogo />
            <Navigation />
          </div>
          <div className="bg-white/30 p-1.5 rounded-full flex items-center justify-center">
            <ClerkLoaded>
              <UserButton
                afterSignOutUrl="/"
                appearance={{
                  elements: {
                    userButtonAvatarBox: {
                      borderRadius: "50%",
                      height: "40px !important",
                      width: "40px !important",
                    },
                  },
                }}
              />
            </ClerkLoaded>
            <ClerkLoading>
                <LoaderIcon className="animate-spin text-slate-400 size-8" />
            </ClerkLoading>
          </div>
        </div>
        <WelcomeMsg />
      </div>
    </header>
  );
};
