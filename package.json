{"name": "finance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate:pg --schema db/schema.ts --out ./drizzle", "db:migrate": "tsx ./scripts/migrate.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@clerk/backend": "^2.0.0", "@clerk/nextjs": "^6.21.0", "@hono/clerk-auth": "^2.0.1", "@hono/zod-validator": "^0.7.0", "@hookform/resolvers": "^5.1.1", "@neondatabase/serverless": "^0.9.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.35.1", "@tanstack/react-table": "^8.16.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "drizzle-orm": "^0.30.10", "drizzle-zod": "^0.5.1", "embla-carousel-react": "^8.6.0", "hono": "^4.3.2", "input-otp": "^1.4.2", "lucide-react": "^0.514.0", "next": "14.2.3", "next-themes": "^0.4.6", "react": "19.0.0-rc-66855b96-20241106", "react-currency-input-field": "^3.8.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.57.0", "react-papaparse": "^4.4.0", "react-resizable-panels": "^3.0.2", "react-select": "^5.8.0", "react-use": "^17.6.0", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "vaul": "^1.1.2", "zod": "^3.25.64", "zustand": "^4.5.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "dotenv": "^16.4.5", "drizzle-kit": "^0.20.17", "eslint": "^8", "eslint-config-next": "15.0.3", "pg": "^8.11.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}