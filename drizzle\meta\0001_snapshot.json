{"id": "ad4fba00-1c04-4879-b889-0e73ef6ed3f9", "prevId": "500538aa-dd02-4769-9e43-9c96e02ae8df", "version": "5", "dialect": "pg", "tables": {"accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "plaid_id": {"name": "plaid_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}